/**
 * Lien Companion → Gabin
 * Transmet les commandes de Companion vers Gabin
 */

import { IModuleLink, LinkConfig } from '../interfaces/link.interface';
import { IModule, IMicrophoneModule, IAutocamModule, IControlModule } from '../interfaces/module.interface';

export class CompanionToGabinLink implements IModuleLink {
    public readonly name = 'companion-to-gabin';
    public readonly description = 'Transmet les commandes Companion vers Gabin';
    public readonly enabled: boolean;

    private companionModule?: IControlModule;
    private gabinModule?: IMicrophoneModule & IAutocamModule;
    private cleanupCallbacks: Array<() => void> = [];

    constructor(private config: LinkConfig) {
        this.enabled = config.enabled;
    }

    async initialize(modules: Map<string, IModule>): Promise<void> {
        if (!this.enabled) {
            console.log(`[${this.name}] Link is disabled`);
            return;
        }

        // Récupérer les modules nécessaires
        this.companionModule = modules.get('companion') as IControlModule;
        this.gabinModule = modules.get('gabin') as IMicrophoneModule & IAutocamModule;

        if (!this.companionModule) {
            console.log(`[${this.name}] Companion module not available - link disabled`);
            return;
        }

        if (!this.gabinModule) {
            console.log(`[${this.name}] Gabin module not available - link disabled`);
            return;
        }

        console.log(`[${this.name}] Initializing link between Companion and Gabin`);

        // S'abonner aux événements de Companion
        this.setupCompanionListeners();

        console.log(`[${this.name}] Link initialized successfully`);
    }

    async cleanup(): Promise<void> {
        console.log(`[${this.name}] Cleaning up link`);
        
        // Nettoyer tous les listeners
        this.cleanupCallbacks.forEach(cleanup => cleanup());
        this.cleanupCallbacks = [];

        this.companionModule = undefined;
        this.gabinModule = undefined;
    }

    /**
     * Configurer les listeners sur les événements Companion
     */
    private setupCompanionListeners(): void {
        if (!this.companionModule || !this.gabinModule) return;

        // Écouter les actions de Companion
        const actionListener = (event: any) => {
            if (event.type === 'companion_action') {
                this.handleCompanionAction(event.data);
            }
        };

        this.companionModule.onEvent(actionListener);

        // Stocker les callbacks pour le nettoyage
        this.cleanupCallbacks.push(
            () => console.log(`[${this.name}] Cleaned up Companion listeners`)
        );
    }

    /**
     * Traiter une action reçue de Companion
     */
    private async handleCompanionAction(actionData: any): Promise<void> {
        if (!this.gabinModule) return;

        const { actionType, target, value } = actionData;

        try {
            // Parser le type d'action (ex: "autocam/toggle", "mic/toggle")
            const [deviceType, action] = actionType.split('/');

            switch (deviceType) {
                case 'autocam':
                    await this.handleAutocamAction(action, value);
                    break;

                case 'mic':
                    await this.handleMicAction(target, action, value);
                    break;

                default:
                    console.log(`[${this.name}] Unhandled device type: ${deviceType}`);
            }
        } catch (error) {
            console.error(`[${this.name}] Error handling action:`, error.message);
        }
    }

    /**
     * Traiter une action autocam
     */
    private async handleAutocamAction(action: string, value: any): Promise<void> {
        if (!this.gabinModule || !this.config.options?.allowAutocamControl) {
            return;
        }

        try {
            if (action === 'toggle') {
                // Basculer l'état actuel
                const currentState = this.gabinModule.getAutocamState();
                const newState = currentState === null ? true : !currentState;
                await this.gabinModule.setAutocamState?.(newState);
                console.log(`[${this.name}] Autocam toggled: ${currentState} → ${newState}`);
            } else if (action === 'on') {
                await this.gabinModule.setAutocamState?.(true);
                console.log(`[${this.name}] Autocam set to: ON`);
            } else if (action === 'off') {
                await this.gabinModule.setAutocamState?.(false);
                console.log(`[${this.name}] Autocam set to: OFF`);
            } else if (typeof value === 'boolean') {
                // Définir un état spécifique via la valeur
                await this.gabinModule.setAutocamState?.(value);
                console.log(`[${this.name}] Autocam set to: ${value}`);
            }
        } catch (error) {
            console.error(`[${this.name}] Error controlling autocam:`, error.message);
        }
    }

    /**
     * Traiter une action micro
     */
    private async handleMicAction(micName: string, action: string, value: any): Promise<void> {
        if (!this.gabinModule || !this.config.options?.allowMicControl || !micName) {
            return;
        }

        try {
            if (action === 'toggle') {
                // Basculer l'état actuel
                const currentState = this.gabinModule.getMicState(micName);
                const newState = currentState === undefined ? true : !currentState;
                await this.gabinModule.setMicState?.(micName, newState);
                console.log(`[${this.name}] Mic '${micName}' toggled: ${currentState} → ${newState}`);
            } else if (action === 'on') {
                await this.gabinModule.setMicState?.(micName, true);
                console.log(`[${this.name}] Mic '${micName}' set to: ON`);
            } else if (action === 'off') {
                await this.gabinModule.setMicState?.(micName, false);
                console.log(`[${this.name}] Mic '${micName}' set to: OFF`);
            } else if (typeof value === 'boolean') {
                // Définir un état spécifique via la valeur
                await this.gabinModule.setMicState?.(micName, value);
                console.log(`[${this.name}] Mic '${micName}' set to: ${value}`);
            }
        } catch (error) {
            console.error(`[${this.name}] Error controlling mic '${micName}':`, error.message);
        }
    }
}
