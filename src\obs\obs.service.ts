import { Injectable } from '@nestjs/common';
import { HubService } from '../core/hub.service';
import { OBSVolumeAdjustment, OBSAudioSource, OBSEventCallback } from './obs.types';

/**
 * Service principal pour la gestion d'OBS Studio
 * Responsabilité : Logique métier et interface pour les contrôles OBS
 */
@Injectable()
export class OBSService {
    private eventCallbacks: Array<OBSEventCallback> = [];
    private transportService: any; // Injecté manuellement pour éviter les dépendances circulaires

    constructor(private readonly hubService: HubService) {
        // S'abonner aux événements du Hub pour maintenir la compatibilité
        this.hubService.onEvent((_event) => {
            // Ici on pourrait écouter des événements du Hub pour déclencher des actions OBS
            // Par exemple : mic activé -> ajuster volume automatiquement
        });
    }

    /**
     * S'abonner aux événements OBS
     */
    onEvent(callback: OBSEventCallback): void {
        this.eventCallbacks.push(callback);
    }

    /**
     * Émettre un événement OBS vers tous les listeners
     */
    private emitEvent(type: 'connection_changed' | 'volume_changed' | 'mute_changed', data: any): void {
        const event = { type, data };
        this.eventCallbacks.forEach((callback) => {
            try {
                callback(event);
            } catch (error) {
                console.error('[obs] Error in event callback:', error);
            }
        });
    }

    // === CONTRÔLES AUDIO ===

    /**
     * Ajuster le volume d'une source audio
     */
    async setSourceVolume(adjustment: OBSVolumeAdjustment): Promise<void> {
        const transport = this.getTransport();
        if (!transport?.isConnected()) {
            throw new Error('OBS not connected');
        }

        try {
            let volumeDb: number;

            // Convertir le volume selon le format fourni
            if (adjustment.volumeDb !== undefined) {
                volumeDb = Math.max(-100, Math.min(26, adjustment.volumeDb));
            } else if (adjustment.volumePercent !== undefined) {
                // Conversion pourcentage vers dB (approximation)
                const percent = Math.max(0, Math.min(100, adjustment.volumePercent));
                volumeDb = percent === 0 ? -100 : (percent - 100) * 0.6; // Approximation
            } else if (adjustment.volumeMul !== undefined) {
                // Conversion multiplier vers dB
                const mul = Math.max(0, adjustment.volumeMul);
                volumeDb = mul === 0 ? -100 : 20 * Math.log10(mul);
            } else {
                throw new Error('No volume value provided');
            }

            await transport.getOBSInstance().call('SetInputVolume', {
                inputName: adjustment.sourceName,
                inputVolumeDb: volumeDb,
            });

            console.log(`[obs] Set volume for "${adjustment.sourceName}" to ${volumeDb.toFixed(1)}dB`);
        } catch (error) {
            console.error(`[obs] Failed to set volume for "${adjustment.sourceName}":`, error.message);
            throw error;
        }
    }

    /**
     * Ajuster le volume d'une source par pourcentage (comme dans Companion)
     */
    async adjustSourceVolumePercent(sourceName: string, percentAdjustment: number): Promise<void> {
        const transport = this.getTransport();
        if (!transport?.isConnected()) {
            throw new Error('OBS not connected');
        }

        try {
            // Récupérer le volume actuel
            const currentInfo = await transport.getOBSInstance().call('GetInputVolume', {
                inputName: sourceName,
            });

            // Calculer le nouveau volume
            const currentMul = currentInfo.inputVolumeMul;

            // Appliquer l'ajustement en pourcentage
            const newMul = currentMul * (1 + percentAdjustment / 100);
            const newDb = newMul === 0 ? -100 : 20 * Math.log10(Math.max(0.001, newMul));

            await this.setSourceVolume({
                sourceName,
                volumeDb: Math.max(-100, Math.min(26, newDb)),
            });
        } catch (error) {
            console.error(`[obs] Failed to adjust volume for "${sourceName}":`, error.message);
            throw error;
        }
    }

    /**
     * Activer/désactiver le mute d'une source
     */
    async setSourceMute(sourceName: string, muted: boolean): Promise<void> {
        const transport = this.getTransport();
        if (!transport?.isConnected()) {
            throw new Error('OBS not connected');
        }

        try {
            await transport.getOBSInstance().call('SetInputMute', {
                inputName: sourceName,
                inputMuted: muted,
            });

            console.log(`[obs] ${muted ? 'Muted' : 'Unmuted'} source "${sourceName}"`);
        } catch (error) {
            console.error(`[obs] Failed to set mute for "${sourceName}":`, error.message);
            throw error;
        }
    }

    /**
     * Basculer le mute d'une source
     */
    async toggleSourceMute(sourceName: string): Promise<boolean> {
        const transport = this.getTransport();
        if (!transport?.isConnected()) {
            throw new Error('OBS not connected');
        }

        try {
            const result = await transport.getOBSInstance().call('ToggleInputMute', {
                inputName: sourceName,
            });

            console.log(`[obs] Toggled mute for "${sourceName}" - now ${result.inputMuted ? 'muted' : 'unmuted'}`);
            return result.inputMuted;
        } catch (error) {
            console.error(`[obs] Failed to toggle mute for "${sourceName}":`, error.message);
            throw error;
        }
    }

    /**
     * Obtenir les informations d'une source audio
     */
    async getSourceInfo(sourceName: string): Promise<OBSAudioSource> {
        const transport = this.getTransport();
        if (!transport?.isConnected()) {
            throw new Error('OBS not connected');
        }

        try {
            const volumeInfo = await transport.getOBSInstance().call('GetInputVolume', {
                inputName: sourceName,
            });

            const muteInfo = await transport.getOBSInstance().call('GetInputMute', {
                inputName: sourceName,
            });

            return {
                inputName: sourceName,
                inputVolumeMul: volumeInfo.inputVolumeMul,
                inputVolumeDb: volumeInfo.inputVolumeDb,
                inputMuted: muteInfo.inputMuted,
            };
        } catch (error) {
            console.error(`[obs] Failed to get info for "${sourceName}":`, error.message);
            throw error;
        }
    }

    // === GESTIONNAIRES D'ÉVÉNEMENTS ===

    /**
     * Gestionnaire pour les changements de volume (appelé par le transport)
     */
    handleVolumeChanged(sourceName: string, volumeDb: number, _volumeMul: number): void {
        console.log(`[obs] Volume changed for "${sourceName}": ${volumeDb.toFixed(1)}dB`);

        this.emitEvent('volume_changed', {
            sourceName,
            volume: volumeDb,
        });
    }

    /**
     * Gestionnaire pour les changements de mute (appelé par le transport)
     */
    handleMuteChanged(sourceName: string, muted: boolean): void {
        console.log(`[obs] Mute changed for "${sourceName}": ${muted ? 'muted' : 'unmuted'}`);

        this.emitEvent('mute_changed', {
            sourceName,
            muted,
        });
    }

    // === UTILITAIRES ===

    /**
     * Injecter le service de transport (appelé par le transport lors de l'initialisation)
     */
    setTransportService(transport: any): void {
        this.transportService = transport;
    }

    /**
     * Obtenir le service de transport
     */
    private getTransport(): any {
        if (!this.transportService) {
            throw new Error('Transport service not initialized');
        }
        return this.transportService;
    }
}
