# Configuration générale pour obs-video-hub
# Copiez ce fichier vers .env et modifiez les valeurs selon vos besoins

# === Configuration générale de l'application ===
# Port du serveur HTTP
HTTP_PORT=3000

# Environnement d'exécution
NODE_ENV=development

# === Configuration des services ===
# Les configurations spécifiques à Gabin et Companion sont maintenant gérées
# dans les fichiers src/config/local/*.local.ts (auto-générés au premier démarrage)
#
# 🔧 Pour personnaliser :
#    1. Démarrez l'application une fois pour générer les fichiers locaux
#    2. Modifiez src/config/local/gabin.local.ts pour Gabin
#    3. Modifiez src/config/local/companion.local.ts pour Companion
#    4. Ces fichiers sont ignorés par Git et contiennent des instructions détaillées
