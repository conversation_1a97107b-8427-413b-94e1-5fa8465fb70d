/**
 * ⚠️  CONFIGURATION PAR DÉFAUT COMPANION - NE PAS MODIFIER
 *
 * Ce fichier contient les valeurs par défaut pour Companion et est versionné dans Git.
 *
 * 🔧 Pour personnaliser la configuration :
 *    1. Les fichiers locaux sont auto-générés au premier démarrage
 *    2. Modifiez src/config/local/companion.local.ts (ignoré par Git)
 *    3. Vos modifications locales surchargent ces valeurs par défaut
 *
 * 🔄 Pour restaurer les valeurs par défaut :
 *    Supprimez src/config/local/companion.local.ts et redémarrez l'application
 */

import { CompanionConfig } from '../../companion/companion.types';

export const companionDefaultConfig: CompanionConfig = {
    network: {
        listenPort: 33223,
        host: '127.0.0.1',
        port: 33224,
    },
    timing: {
        connectionCheckInterval: 6000,
        initialStateDelay: 1000,
    },
    feedbacks: [
        // Autocam
        {
            type: 'autocam',
            path: '/autocam',
        },
        // Micros
        {
            type: 'mic',
            path: '/mic/MIC1',
            micName: 'MIC1',
        },
        {
            type: 'mic',
            path: '/mic/MIC2',
            micName: 'MIC2',
        },
    ],
};
