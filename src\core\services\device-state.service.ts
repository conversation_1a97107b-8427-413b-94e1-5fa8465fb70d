import { Injectable } from '@nestjs/common';
import { HubEventsService } from './hub-events.service';
import { modulesConfig } from '../../config';

/**
 * Service dédié à la gestion des états des appareils (autocam et micros)
 * Responsabilité : Gestion centralisée des états des fonctionnalités hardware
 *
 * Note: Fonctionne avec ou sans Gabin selon la configuration
 */
@Injectable()
export class DeviceStateService {
    private autocamState: boolean | null = null;
    private micStates: Record<string, boolean> = {};

    constructor(private readonly hubEventsService: HubEventsService) {}

    // === GESTION DE L'AUTOCAM ===

    /**
     * Met à jour l'état de l'autocam
     */
    setAutocam(value: boolean): void {
        if (this.autocamState !== value) {
            this.autocamState = value;
            console.log(`[DeviceState] Autocam changed to: ${value}`);

            this.hubEventsService.emitEvent({
                type: 'autocam_changed',
                data: { value },
            });
        }
    }

    /**
     * Récupère l'état de l'autocam
     */
    getAutocam(): boolean | null {
        return this.autocamState;
    }

    // === GESTION DES MICROS ===

    /**
     * Met à jour l'état d'un micro
     */
    setMic(name: string, value: boolean): void {
        if (this.micStates[name] !== value) {
            this.micStates[name] = value;
            console.log(`[DeviceState] Mic '${name}' changed to: ${value}`);

            this.hubEventsService.emitEvent({
                type: 'mic_changed',
                data: { name, value },
            });
        }
    }

    /**
     * Récupère l'état d'un micro
     */
    getMic(name: string): boolean | undefined {
        return this.micStates[name];
    }

    /**
     * Récupère tous les états des micros
     */
    getAllMics(): Record<string, boolean> {
        return { ...this.micStates };
    }

    // === GESTION ALTERNATIVE SANS GABIN ===

    /**
     * Initialise les états des micros depuis OBS (quand Gabin est désactivé)
     * Cette méthode sera appelée par le module OBS
     */
    setMicFromOBS(sourceName: string, muted: boolean): void {
        // Inverser la logique : muted = false signifie micro actif
        const micActive = !muted;

        if (!modulesConfig.gabin.enabled) {
            this.setMic(sourceName, micActive);
        }
    }

    /**
     * Réinitialise tous les états (utile lors du changement de configuration)
     */
    resetAllStates(): void {
        console.log('[DeviceState] Resetting all device states');
        this.autocamState = null;
        this.micStates = {};
    }

    /**
     * Récupère l'état complet des appareils
     */
    getDeviceStates(): { autocam: boolean | null; mics: Record<string, boolean> } {
        return {
            autocam: this.autocamState,
            mics: { ...this.micStates },
        };
    }
}
