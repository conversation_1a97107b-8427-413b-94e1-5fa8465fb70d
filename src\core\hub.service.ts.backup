import { Injectable } from '@nestjs/common';
import { HubState, HubEventCallback, ServiceName, ServiceStatus } from './hub.types';
import { HubEventsService } from './services/hub-events.service';
import { DeviceStateService } from './services/device-state.service';
import { ServiceStateService } from './services/service-state.service';

/**
 * Service central qui orchestre les communications entre services
 * Responsabilité : Orchestration et interface unifiée pour les autres services
 */
/**
 * Service central qui orchestre les communications entre services
 * Responsabilité : Orchestration et interface unifiée pour les autres services
 */
@Injectable()
export class HubService {
    constructor(
        private readonly hubEventsService: HubEventsService,
        private readonly deviceStateService: DeviceStateService,
        private readonly serviceStateService: ServiceStateService,
    ) {}

    /**
     * S'abonner aux événements du Hub
     */
    onEvent(callback: HubEventCallback): void {
        this.hubEventsService.onEvent(callback);
    }

    // === GESTION DE L'AUTOCAM ===

    /**
     * Met à jour l'état de l'autocam
     */
    setAutocam(value: boolean): void {
        this.deviceStateService.setAutocam(value);
    }

    /**
     * Récupère l'état de l'autocam
     */
    getAutocam(): boolean | null {
        return this.deviceStateService.getAutocam();
    }

    // === GESTION DES MICROS ===

    /**
     * Met à jour l'état d'un micro
     */
    setMic(name: string, value: boolean): void {
        this.deviceStateService.setMic(name, value);
    }

    /**
     * Récupère l'état d'un micro
     */
    getMic(name: string): boolean | undefined {
        return this.deviceStateService.getMic(name);
    }

    /**
     * Récupère tous les états des micros
     */
    getAllMics(): Record<string, boolean> {
        return this.deviceStateService.getAllMics();
    }

    // === GESTION DES SERVICES ===

    /**
     * Met à jour le statut d'un service
     */
    setServiceStatus(serviceName: ServiceName, status: Partial<ServiceStatus>): void {
        this.serviceStateService.setServiceStatus(serviceName, status);
    }

    /**
     * Stocke le timeout d'un service
     */
    setServiceTimeout(serviceName: ServiceName, timeoutId: NodeJS.Timeout): void {
        this.serviceStateService.setServiceTimeout(serviceName, timeoutId);
    }

    /**
     * Récupère le statut d'un service
     */
    getServiceStatus(serviceName: ServiceName): ServiceStatus {
        return this.serviceStateService.getServiceStatus(serviceName);
    }

    /**
     * Vérifie si un service est connecté
     */
    isServiceConnected(serviceName: ServiceName): boolean {
        return this.serviceStateService.isServiceConnected(serviceName);
    }

    // === STATUS JSON ===

    /**
     * Récupère l'état complet pour l'endpoint /status
     */
    getStatus(): HubState {
        const deviceStates = this.deviceStateService.getDeviceStates();
        const serviceStates = this.serviceStateService.getAllServiceStates();

        return {
            autocam: deviceStates.autocam,
            mics: deviceStates.mics,
            services: serviceStates,
        };
    }
}
