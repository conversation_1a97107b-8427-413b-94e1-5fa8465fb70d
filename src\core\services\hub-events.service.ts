import { Injectable } from '@nestjs/common';
import { HubEvent, HubEventCallback } from '../hub.types';

/**
 * Service dédié à la gestion des événements du Hub
 * Responsabilité : Système d'événements centralisé pour la communication inter-services
 */
@Injectable()
export class HubEventsService {
    private eventListeners: Array<HubEventCallback> = [];

    /**
     * S'abonner aux événements du Hub
     */
    onEvent(callback: HubEventCallback): void {
        this.eventListeners.push(callback);
    }

    /**
     * Émettre un événement vers tous les listeners
     */
    emitEvent(event: HubEvent): void {
        this.eventListeners.forEach((callback) => {
            try {
                callback(event);
            } catch (error) {
                console.error('[HubEvents] Error in event callback:', error);
            }
        });
    }

    /**
     * Obtenir le nombre de listeners enregistrés (pour debug/monitoring)
     */
    getListenerCount(): number {
        return this.eventListeners.length;
    }
}
