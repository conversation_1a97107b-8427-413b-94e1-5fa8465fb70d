/**
 * Interfaces communes pour tous les modules du Hub
 * Chaque module doit implémenter ces interfaces pour être autonome
 */

/**
 * État de connexion d'un module
 */
export interface ModuleConnectionStatus {
    connected: boolean;
    lastSeen?: Date;
    error?: string;
}

/**
 * Configuration de base pour un module
 */
export interface ModuleConfig {
    enabled: boolean;
    description?: string;
}

/**
 * Événement émis par un module
 */
export interface ModuleEvent<T = any> {
    type: string;
    source: string; // Nom du module qui émet l'événement
    data: T;
    timestamp: Date;
}

/**
 * Callback pour écouter les événements d'un module
 */
export type ModuleEventCallback<T = any> = (event: ModuleEvent<T>) => void;

/**
 * Interface de base que tous les modules doivent implémenter
 */
export interface IModule {
    /**
     * Nom du module
     */
    readonly name: string;

    /**
     * Indique si le module est activé
     */
    readonly enabled: boolean;

    /**
     * État de connexion du module
     */
    getConnectionStatus(): ModuleConnectionStatus;

    /**
     * S'abonner aux événements du module
     */
    onEvent<T = any>(callback: ModuleEventCallback<T>): void;

    /**
     * Démarrer le module (connexions, etc.)
     */
    start(): Promise<void>;

    /**
     * Arrêter le module
     */
    stop(): Promise<void>;

    /**
     * Obtenir l'état complet du module
     */
    getState(): Record<string, any>;
}

/**
 * Interface pour les modules qui gèrent des micros
 */
export interface IMicrophoneModule extends IModule {
    /**
     * Obtenir l'état d'un micro
     */
    getMicState(micName: string): boolean | undefined;

    /**
     * Obtenir tous les états des micros
     */
    getAllMicStates(): Record<string, boolean>;

    /**
     * Définir l'état d'un micro (si le module le supporte)
     */
    setMicState?(micName: string, active: boolean): Promise<void>;
}

/**
 * Interface pour les modules qui gèrent l'autocam
 */
export interface IAutocamModule extends IModule {
    /**
     * Obtenir l'état de l'autocam
     */
    getAutocamState(): boolean | null;

    /**
     * Définir l'état de l'autocam (si le module le supporte)
     */
    setAutocamState?(active: boolean): Promise<void>;
}

/**
 * Interface pour les modules qui gèrent des contrôles (comme Companion)
 */
export interface IControlModule extends IModule {
    /**
     * Envoyer un feedback vers le module de contrôle
     */
    sendFeedback?(type: string, value: any, target?: string): Promise<void>;
}

/**
 * Types d'événements standards
 */
export const MODULE_EVENTS = {
    CONNECTION_CHANGED: 'connection_changed',
    MIC_STATE_CHANGED: 'mic_state_changed',
    AUTOCAM_STATE_CHANGED: 'autocam_state_changed',
    ERROR: 'error',
} as const;

export type ModuleEventType = typeof MODULE_EVENTS[keyof typeof MODULE_EVENTS];
