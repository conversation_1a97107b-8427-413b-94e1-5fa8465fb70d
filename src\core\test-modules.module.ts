import { <PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { GabinModuleService } from '../gabin/gabin-module.service';
import { OBSModuleService } from '../obs/obs-module.service';
import { CompanionModuleService } from '../companion/companion-module.service';
import { LinkManagerService } from './services/link-manager.service';

/**
 * Module de test pour les nouveaux modules autonomes
 * Permet de tester la nouvelle architecture en parallèle de l'ancienne
 */
@Module({
    providers: [
        GabinModuleService,
        OBSModuleService, 
        CompanionModuleService,
        LinkManagerService,
    ],
    exports: [
        GabinModuleService,
        OBSModuleService,
        CompanionModuleService,
    ],
})
export class TestModulesModule implements OnModuleInit {
    constructor(
        private readonly linkManager: LinkManagerService,
        private readonly gabinModule: GabinModuleService,
        private readonly obsModule: OBSModuleService,
        private readonly companionModule: CompanionModuleService,
    ) {}

    async onModuleInit() {
        console.log('[TestModules] Initializing new autonomous modules...');
        
        // Enregistrer les modules dans le LinkManager
        this.linkManager.registerModule(this.gabinModule);
        this.linkManager.registerModule(this.obsModule);
        this.linkManager.registerModule(this.companionModule);
        
        console.log('[TestModules] New autonomous modules registered');
    }
}
