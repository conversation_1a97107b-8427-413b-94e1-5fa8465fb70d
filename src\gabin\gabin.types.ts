/**
 * Types et interfaces pour le module Gabin
 */

/**
 * Configuration d'un type de registration OSC vers Gabin
 */
export interface GabinRegistrationType {
    type: string;
    path: string;
}

/**
 * Types de registrations supportées par Gabin
 */
export type GabinRegisterType = 'autocam' | 'micFeedback';

/**
 * Callback pour les commandes autocam
 */
export type AutocamCommandCallback = (value: boolean) => void;

/**
 * Callback pour les commandes micro
 */
export type MicCommandCallback = (name: string, value: boolean) => void;

/**
 * Configuration réseau pour Gabin
 */
export interface GabinNetworkConfig {
    listenPort: number;
    sendPort: number;
    sendHost: string;
}

/**
 * Configuration des timeouts et intervalles pour Gabin
 */
export interface GabinTimingConfig {
    pingInterval: number;
    pingTimeout: number;
    reconnectionDelay: number;
}

/**
 * Configuration complète pour Gabin
 */
export interface GabinConfig {
    network: GabinNetworkConfig;
    timing: GabinTimingConfig;
    registrations: GabinRegistrationType[];
}
