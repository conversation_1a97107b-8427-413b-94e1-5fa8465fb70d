# Intégration Companion OSC

Ce document explique comment utiliser l'intégration OSC avec Bitfocus Companion pour recevoir le statut de l'autocam et des micros depuis Gabin.

## Architecture

```
Gabin ↔ obs-video-hub ↔ Companion
```

1. **Gabin** envoie les changements d'état via OSC vers `obs-video-hub`
2. **obs-video-hub** traite ces changements et les redistribue **automatiquement** vers Companion
3. **Companion** reçoit les mises à jour de statut pour ses boutons/feedbacks
4. **Companion** peut envoyer des commandes de contrôle vers `obs-video-hub` qui les transmet à Gabin

## ✨ Nouveauté : Feedbacks automatiques

**Plus besoin d'enregistrement manuel !** Le système envoie automatiquement les feedbacks vers Companion selon la configuration définie dans `companion.config.ts`.

## Configuration des ports

- **Gabin → obs-video-hub** : Port 33123 (écoute)
- **obs-video-hub → Gabin** : Port 32123 (envoi des registrations)
- **Companion → obs-video-hub** : Port 33223 (écoute des registrations)
- **obs-video-hub → Companion** : Port configuré par Companion (feedback)

## Configuration

### 1. Configuration du hub (companion.config.ts)

Modifiez le fichier `src/companion/companion.config.ts` pour définir vos targets :

```typescript
export const companionConfig: CompanionConfig = {
    host: '127.0.0.1',  // IP de votre Companion
    port: 32500,        // Port OSC de Companion

    feedbacks: [
        // Autocam
        { type: 'autocam', path: '/feedback/autocam' },

        // Micros
        { type: 'mic', path: '/feedback/mic/MIC1', micName: 'MIC1' },
        { type: 'mic', path: '/feedback/mic/MIC2', micName: 'MIC2' },
        // Ajouter d'autres micros selon vos besoins
    ]
};
```

### 2. Configuration dans Companion

1. Créer une instance OSC dans Companion
2. Configurer l'instance pour envoyer vers `127.0.0.1:33223` (pour les actions)
3. Configurer l'instance pour écouter sur le port `32500` (pour les feedbacks)
4. Créer des boutons avec actions ET feedbacks

### 3. Signal de démarrage (optionnel)

Si Companion démarre après obs-video-hub, il peut envoyer un signal pour recevoir immédiatement tous les états actuels :

```javascript
// Message à envoyer depuis Companion vers obs-video-hub
Path: /companion/ready
Args: []

// obs-video-hub enverra alors tous les états actuels
```

### 4. Réception automatique des feedbacks

Companion recevra automatiquement tous les feedbacks configurés :

```javascript
// Messages reçus automatiquement par Companion
Path: /feedback/autocam
Args: [1]  // 1 = ON, 0 = OFF, 2 = UNDEFINED (Gabin déconnecté)

Path: /feedback/mic/MIC1
Args: [1]  // 1 = ON, 0 = OFF, 2 = UNDEFINED (Gabin déconnecté)

Path: /feedback/mic/MIC2
Args: [0]  // 1 = ON, 0 = OFF, 2 = UNDEFINED (Gabin déconnecté)
```

### 5. États des boutons selon la connexion Gabin

| Valeur | État | Couleur bouton | Signification |
|--------|------|----------------|---------------|
| `0` | OFF | Rouge | Gabin connecté, élément éteint |
| `1` | ON | Vert | Gabin connecté, élément allumé |
| `2` | UNDEFINED | Gris | Gabin déconnecté, état inconnu |

**Système de ping automatique :**
- obs-video-hub ping Gabin toutes les 5 secondes via `/gabin/is-ready`
- Si Gabin répond : boutons affichent les vrais états (0/1)
- Si Gabin ne répond pas : boutons deviennent gris (2)

**Avantages :**
- ✅ Pas de registration manuelle nécessaire
- ✅ Feedbacks envoyés dès le démarrage du hub
- ✅ Synchronisation garantie même si Companion redémarre
- ✅ Configuration centralisée dans le hub
- ✅ Détection automatique de l'état de connexion Gabin
- ✅ Boutons gris quand Gabin est déconnecté

## Messages OSC supportés

### Signal de démarrage (Companion → obs-video-hub)

| Path | Description | Arguments |
|------|-------------|-----------|
| `/companion/ready` | Signaler que Companion est prêt à recevoir les feedbacks | `[]` |

### Actions/Contrôles (Companion → obs-video-hub)

| Path | Description | Arguments |
|------|-------------|-----------|
| `/action/autocam/toggle` | Toggle l'état de l'autocam | `[]` |
| `/action/autocam/on` | Allumer l'autocam | `[]` |
| `/action/autocam/off` | Éteindre l'autocam | `[]` |
| `/action/mic/{mic_name}/toggle` | Toggle l'état d'un micro | `[]` |
| `/action/mic/{mic_name}/on` | Allumer un micro | `[]` |
| `/action/mic/{mic_name}/off` | Éteindre un micro | `[]` |

### Feedbacks (obs-video-hub → Companion)

| Path | Description | Arguments |
|------|-------------|-----------|
| `{feedback_path}` | État de l'autocam ou du micro | `[0|1]` |

## Exemple de configuration Companion

### Bouton avec feedback ET contrôle

#### Bouton Autocam Toggle
1. **Action** :
   - Type : OSC
   - Path : `/action/autocam/toggle`
   - Host : `127.0.0.1`
   - Port : `33223`
2. **Feedback** :
   - Type : OSC
   - Path : `/feedback/autocam`
   - Condition : `$(internal:osc_/feedback/autocam) == 1`
   - Style : Couleur verte quand actif

#### Bouton Micro Toggle
1. **Action** :
   - Type : OSC
   - Path : `/action/mic/MIC1/toggle`
   - Host : `127.0.0.1`
   - Port : `33223`
2. **Feedback** :
   - Type : OSC
   - Path : `/feedback/mic/MIC1`
   - Condition : `$(internal:osc_/feedback/mic/MIC1) == 1`
   - Style : Couleur rouge quand actif (micro ouvert)

### Script d'initialisation

```javascript
// À exécuter au démarrage de Companion pour s'enregistrer

// Registration pour l'autocam
osc.send('127.0.0.1', 33223, '/register/autocam', [
    '127.0.0.1',           // Host où Companion écoute
    32500,                 // Port où Companion écoute
    '/feedback/autocam'    // Path du feedback
]);

// Registration pour plusieurs micros
osc.send('127.0.0.1', 33223, '/register/mic/MIC1', [
    '127.0.0.1',
    32500,
    '/feedback/mic/MIC1'
]);

osc.send('127.0.0.1', 33223, '/register/mic/MIC2', [
    '127.0.0.1',
    32500,
    '/feedback/mic/MIC2'
]);
```

## Flux de contrôle bidirectionnel

### 🎯 Contrôle (Companion → Gabin)
```
1. Companion Button Press
2. OSC Action (/action/autocam/toggle) → obs-video-hub:33223
3. CompanionTransportService.handleActionMessage()
4. GabinService.sendAutocamCommand()
5. GabinTransportService.sendAutocamCommand()
6. OSC Command → Gabin:32123
7. Gabin Hardware executes action
```

### 📡 Feedback (Gabin → Companion)
```
1. Gabin Hardware state change
2. OSC Feedback → obs-video-hub:33123
3. GabinTransportService receives feedback
4. GabinService.setAutocam() (update local state)
5. Notify callbacks → CompanionTransportService
6. OSC Feedback → Companion:32500
7. Companion Button updates display
```

### 🔄 Cycle complet
1. **Utilisateur** appuie sur bouton Companion
2. **Companion** envoie action OSC vers obs-video-hub
3. **obs-video-hub** traite l'action et envoie commande vers Gabin
4. **Gabin** exécute l'action physique
5. **Gabin** renvoie le nouvel état vers obs-video-hub
6. **obs-video-hub** met à jour son état local et notifie Companion
7. **Companion** met à jour l'affichage du bouton

## Débogage

### Logs côté obs-video-hub

```
[Companion] OSC server listening on port 33223
[Companion] Received message: /register/autocam
[Companion] Registered autocam feedback at 127.0.0.1:32500/feedback/autocam
[Companion] Autocam state changed: true
[Companion] Sending autocam feedback: true to 1 targets
[Companion] Sent autocam feedback (1) to 127.0.0.1:32500/feedback/autocam
```

### Vérification de l'état

Endpoint HTTP pour vérifier l'état actuel :
```
GET http://localhost:3000/status
```

Réponse :
```json
{
  "autocam": true,
  "mics": {
    "mic1": true,
    "mic2": false
  }
}
```

## Dépannage

### Companion ne reçoit pas les feedbacks

1. Vérifier que Companion s'est bien enregistré (voir logs)
2. Vérifier que les ports sont corrects
3. Vérifier que l'état de l'autocam n'est pas `null`

### obs-video-hub ne reçoit pas les données de Gabin

1. Vérifier que Gabin envoie vers le bon port (33123)
2. Vérifier les logs de registration Gabin
3. Tester l'endpoint `/status` pour voir l'état actuel

## Test de l'intégration

### Scripts de test disponibles

#### 1. Test des feedbacks automatiques
```bash
# Démarrer obs-video-hub
npm run start:dev

# Dans un autre terminal, tester les feedbacks automatiques
node test-auto-feedback.js
```

Ce script teste :
- ✅ Réception automatique des feedbacks configurés
- ✅ Signal "ready" de Companion
- ✅ Synchronisation lors de changements d'état Gabin
- ✅ Scénario de redémarrage de Companion

#### 2. Test des actions de contrôle
```bash
# Tester le contrôle bidirectionnel
node test-companion-actions.js
```

Ce script teste :
- ✅ Actions de contrôle (toggle, on, off)
- ✅ Feedbacks automatiques en retour
- ✅ Cycle complet Companion → Gabin → Companion

#### 3. Test de la connexion Gabin
```bash
# Tester la détection de connexion/déconnexion Gabin
node test-gabin-connection.js
```

Ce script teste :
- ✅ Ping automatique vers Gabin
- ✅ Détection de connexion/déconnexion
- ✅ Boutons gris quand Gabin est déconnecté
- ✅ Retour aux vrais états quand Gabin se reconnecte

#### 4. Test basique (legacy)
```bash
# Test avec registration manuelle (pour compatibilité)
node test-companion.js
```

### Exemple de sortie attendue

```
=== Test Companion Integration ===

1. Registering for autocam feedback...
✓ Registered for autocam feedback

2. Registering for mic feedback...
✓ Registered for MIC1 feedback

3. Starting feedback listener on port 32500...
✓ Feedback listener started

4. Simulating Gabin state changes...
   → Sending autocam ON
📡 Received feedback: /feedback/autocam = 1
   → Autocam is ON

   → Sending MIC1 ON
📡 Received feedback: /feedback/mic/MIC1 = 1
   → Mic 'MIC1' is ON

   → Sending autocam OFF
📡 Received feedback: /feedback/autocam = 0
   → Autocam is OFF

   → Sending MIC1 OFF
📡 Received feedback: /feedback/mic/MIC1 = 0
   → Mic 'MIC1' is OFF

✓ Test completed
```
