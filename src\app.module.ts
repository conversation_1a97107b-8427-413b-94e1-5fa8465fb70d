import { Module, DynamicModule } from '@nestjs/common';
import { AppController } from './app.controller';
import { HubModule } from './core/hub.module';
import { TestModulesModule } from './core/test-modules.module';
import { GabinModule } from './gabin/gabin.module';
import { CompanionModule } from './companion/companion.module';
import { OBSModule } from './obs/obs.module';
import { modulesConfig } from './config';

@Module({
    controllers: [AppController],
})
export class AppModule {
    static forRoot(): DynamicModule {
        const imports = [HubModule, TestModulesModule];

        // MODE TEST : Charger seulement les nouveaux modules autonomes
        console.log('[app] Loading NEW autonomous modules for testing...');

        // Désactiver temporairement les anciens modules pour tester les nouveaux
        /*
        // Chargement conditionnel des modules selon la configuration
        if (modulesConfig.gabin.enabled) {
            console.log('[app] Loading Gabin module...');
            imports.push(GabinModule);
        } else {
            console.log('[app] Gabin module disabled in configuration');
        }

        if (modulesConfig.companion.enabled) {
            console.log('[app] Loading Companion module...');
            imports.push(CompanionModule);
        } else {
            console.log('[app] Companion module disabled in configuration');
        }

        if (modulesConfig.obs.enabled) {
            console.log('[app] Loading OBS module...');
            imports.push(OBSModule);
        } else {
            console.log('[app] OBS module disabled in configuration');
        }
        */

        return {
            module: AppModule,
            imports,
        };
    }
}
