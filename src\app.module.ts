import { Module, DynamicModule } from '@nestjs/common';
import { AppController } from './app.controller';
import { HubModule } from './core/hub.module';
import { TestModulesModule } from './core/test-modules.module';

@Module({
    controllers: [AppController],
})
export class AppModule {
    static forRoot(): DynamicModule {
        // Nouvelle architecture avec modules autonomes
        console.log('[app] Loading autonomous modules...');

        return {
            module: AppModule,
            imports: [HubModule, TestModulesModule],
        };
    }
}
