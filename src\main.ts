import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ConfigGenerator } from './config/config.generator';

async function bootstrap() {
    // Vérification de la configuration AVANT le démarrage de NestJS
    const isFirstTime = ConfigGenerator.generateMissingLocalConfigs();

    if (isFirstTime) {
        // Si c'est la première fois, le générateur va arrêter l'application
        // On n'initialise pas NestJS pour éviter les logs parasites
        return;
    }

    // Import dynamique de la config seulement si on continue
    const { appConfig } = await import('./config');

    const app = await NestFactory.create(AppModule.forRoot());

    await app.listen(appConfig.http.port);
    console.log(`[http] Listening on http://localhost:${appConfig.http.port}`);
}
bootstrap();
