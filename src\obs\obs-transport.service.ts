import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { OBSWebSocket } from 'obs-websocket-js';
import { OBSService } from './obs.service';
import { HubService } from '../core/hub.service';
import { obsConfig } from '../config';

@Injectable()
export class OBSTransportService implements OnModuleInit, OnModuleDestroy {
    private readonly config = obsConfig;
    private obs: OBSWebSocket;
    private reconnectTimer?: NodeJS.Timeout;
    private pingTimer?: NodeJS.Timeout;
    private isConnecting = false;

    constructor(
        private readonly obsService: OBSService,
        private readonly hubService: HubService,
    ) {
        this.obs = new OBSWebSocket();
        this.setupEventListeners();
    }

    async onModuleInit() {
        console.log('[obs] Initializing OBS WebSocket transport...');

        // Injecter la référence du transport dans le service OBS
        this.obsService.setTransportService(this);

        if (this.config.autoConnect) {
            await this.connect();
        }
    }

    async onModuleDestroy() {
        console.log('[obs] Shutting down OBS WebSocket transport...');
        this.cleanup();
        await this.disconnect();
    }

    /**
     * Se connecter à OBS WebSocket
     */
    async connect(): Promise<void> {
        if (this.isConnecting || this.obs.identified) {
            return;
        }

        this.isConnecting = true;

        try {
            console.log(`[obs] Connecting to OBS at ${this.config.network.host}:${this.config.network.port}...`);

            const url = `ws://${this.config.network.host}:${this.config.network.port}`;
            const connectionInfo = await this.obs.connect(url, this.config.network.password);

            console.log(`[obs] Connected to OBS WebSocket v${connectionInfo.obsWebSocketVersion} (RPC v${connectionInfo.negotiatedRpcVersion})`);

            this.hubService.setServiceStatus('obs', {
                connected: true,
                lastSeen: new Date(),
            });

            this.startPing();
        } catch (error) {
            console.error('[obs] Failed to connect to OBS:', error.message);
            this.hubService.setServiceStatus('obs', {
                connected: false,
                lastSeen: undefined,
            });
            this.scheduleReconnect();
        } finally {
            this.isConnecting = false;
        }
    }

    /**
     * Se déconnecter d'OBS WebSocket
     */
    async disconnect(): Promise<void> {
        this.cleanup();

        if (this.obs.identified) {
            try {
                await this.obs.disconnect();
                console.log('[obs] Disconnected from OBS');
            } catch (error) {
                console.error('[obs] Error during disconnect:', error.message);
            }
        }

        this.hubService.setServiceStatus('obs', {
            connected: false,
            lastSeen: undefined,
        });
    }

    /**
     * Obtenir l'instance OBS WebSocket pour les commandes
     */
    getOBSInstance(): OBSWebSocket {
        return this.obs;
    }

    /**
     * Vérifier si OBS est connecté
     */
    isConnected(): boolean {
        return this.obs.identified;
    }

    /**
     * Configuration des listeners d'événements OBS
     */
    private setupEventListeners(): void {
        // Événements de connexion
        this.obs.on('ConnectionOpened', () => {
            console.log('[obs] WebSocket connection opened');
        });

        this.obs.on('ConnectionClosed', (error) => {
            console.log('[obs] WebSocket connection closed');
            this.hubService.setServiceStatus('obs', {
                connected: false,
                lastSeen: undefined,
            });

            if (error) {
                console.error('[obs] Connection closed with error:', error.message);
                this.scheduleReconnect();
            }
        });

        this.obs.on('ConnectionError', (error) => {
            console.error('[obs] WebSocket connection error:', error.message);
        });

        // Événements audio
        this.obs.on('InputVolumeChanged', (data) => {
            this.obsService.handleVolumeChanged(data.inputName, data.inputVolumeDb, data.inputVolumeMul);
        });

        this.obs.on('InputMuteStateChanged', (data) => {
            this.obsService.handleMuteChanged(data.inputName, data.inputMuted);
        });
    }

    /**
     * Démarrer le système de ping
     */
    private startPing(): void {
        this.pingTimer = setInterval(async () => {
            if (this.obs.identified) {
                try {
                    await this.obs.call('GetVersion');
                    // Ping réussi, pas besoin de log
                } catch (error) {
                    console.error('[obs] Ping failed:', error.message);
                    this.scheduleReconnect();
                }
            }
        }, this.config.timing.pingInterval);
    }

    /**
     * Programmer une reconnexion
     */
    private scheduleReconnect(): void {
        if (this.reconnectTimer) {
            return; // Reconnexion déjà programmée
        }

        console.log(`[obs] Scheduling reconnection in ${this.config.timing.reconnectInterval}ms...`);

        this.reconnectTimer = setTimeout(async () => {
            this.reconnectTimer = undefined;
            await this.connect();
        }, this.config.timing.reconnectInterval);
    }

    /**
     * Nettoyer les timers
     */
    private cleanup(): void {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = undefined;
        }

        if (this.pingTimer) {
            clearInterval(this.pingTimer);
            this.pingTimer = undefined;
        }
    }
}
