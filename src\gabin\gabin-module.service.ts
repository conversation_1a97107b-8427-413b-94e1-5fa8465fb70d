/**
 * Module Gabin autonome
 * Gère sa propre connexion, ses états et sa logique
 */

import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { BaseModule } from '../core/base/base-module';
import { IMicrophoneModule, IAutocamModule, MODULE_EVENTS } from '../core/interfaces/module.interface';
import { gabinConfig, modulesConfig } from '../config';
import { Server, Client } from 'node-osc';

@Injectable()
export class GabinModuleService 
    extends BaseModule 
    implements IMicrophoneModule, IAutocamModule, OnModuleInit, OnModuleDestroy {
    
    private oscServer?: Server;
    private oscClient?: Client;
    private pingInterval?: NodeJS.Timeout;
    private readonly config = gabinConfig;

    // États internes du module
    private autocamState: boolean | null = null;
    private micStates: Record<string, boolean> = {};

    constructor() {
        super('gabin', modulesConfig.gabin.enabled);
        this.log('Gabin module created', { enabled: this.enabled });
    }

    async onModuleInit() {
        if (this.enabled) {
            await this.start();
        } else {
            this.log('Module disabled - not starting');
        }
    }

    async onModuleDestroy() {
        await this.stop();
    }

    async start(): Promise<void> {
        if (!this.enabled) {
            this.log('Cannot start - module is disabled');
            return;
        }

        try {
            this.log('Starting Gabin module...');
            
            await this.startOscServer();
            this.startOscClient();
            this.startConnectionMonitoring();
            
            this.updateConnectionStatus({ connected: true });
            this.log('Gabin module started successfully');
            
        } catch (error) {
            this.handleError(error, 'Failed to start Gabin module');
        }
    }

    async stop(): Promise<void> {
        this.log('Stopping Gabin module...');
        
        if (this.pingInterval) {
            clearInterval(this.pingInterval);
            this.pingInterval = undefined;
        }

        if (this.oscServer) {
            this.oscServer.close();
            this.oscServer = undefined;
        }

        if (this.oscClient) {
            this.oscClient.close();
            this.oscClient = undefined;
        }

        this.updateConnectionStatus({ connected: false });
        this.log('Gabin module stopped');
    }

    // === INTERFACE IMicrophoneModule ===

    getMicState(micName: string): boolean | undefined {
        return this.micStates[micName];
    }

    getAllMicStates(): Record<string, boolean> {
        return { ...this.micStates };
    }

    async setMicState(micName: string, active: boolean): Promise<void> {
        if (!this.isReady()) {
            throw new Error('Gabin module not ready');
        }

        try {
            // Envoyer la commande OSC vers Gabin
            await this.sendOscCommand('/mic/set', [micName, active ? 1 : 0]);

            // Mettre à jour l'état local (sera confirmé par le feedback de Gabin)
            this.updateMicState(micName, active);

        } catch (error) {
            this.handleError(error, `Failed to set mic state: ${micName}`);
            throw error;
        }
    }

    // === INTERFACE IAutocamModule ===

    getAutocamState(): boolean | null {
        return this.autocamState;
    }

    async setAutocamState(active: boolean): Promise<void> {
        if (!this.isReady()) {
            throw new Error('Gabin module not ready');
        }

        try {
            // Envoyer la commande OSC vers Gabin
            await this.sendOscCommand('/autocam/set', [active ? 1 : 0]);

            // Mettre à jour l'état local (sera confirmé par le feedback de Gabin)
            this.updateAutocamState(active);
        } catch (error) {
            this.handleError(error as Error, 'Failed to set autocam state');
            throw error;
        }
    }

    // === MÉTHODES PRIVÉES ===

    private async startOscServer(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.oscServer = new Server(this.config.network.listenPort, '0.0.0.0', () => {
                this.log(`OSC server listening on port ${this.config.network.listenPort}`);
                resolve();
            });

            this.oscServer.on('error', (error) => {
                reject(new Error(`OSC server error: ${error.message}`));
            });

            this.oscServer.on('message', (msg) => {
                this.handleOscMessage(msg);
            });
        });
    }

    private startOscClient(): void {
        this.oscClient = new Client(this.config.network.sendHost, this.config.network.sendPort);
        this.log(`OSC client connected to ${this.config.network.sendHost}:${this.config.network.sendPort}`);
    }

    private startConnectionMonitoring(): void {
        // Envoyer les registrations pour recevoir les feedbacks
        this.sendRegistrations().catch(error => {
            this.handleError(error as Error, 'Failed to send initial registrations');
        });

        // Ping Gabin périodiquement pour vérifier la connexion
        this.pingInterval = setInterval(() => {
            this.pingGabin().catch(error => {
                this.handleError(error as Error, 'Failed to ping Gabin in interval');
            });
        }, this.config.timing.pingInterval);

        // Premier ping immédiat
        this.pingGabin().catch(error => {
            this.handleError(error as Error, 'Failed to send initial ping');
        });
    }

    private async sendRegistrations(): Promise<void> {
        try {
            for (const registration of this.config.registrations) {
                await this.sendOscCommand('/gabin/register', [
                    registration.type,
                    this.config.network.sendHost,
                    this.config.network.listenPort,
                    registration.path
                ]);
                this.log(`Registration sent: ${registration.type} → ${registration.path}`);
            }
        } catch (error) {
            this.handleError(error as Error, 'Failed to send registrations');
        }
    }

    private async pingGabin(): Promise<void> {
        try {
            await this.sendOscCommand('/gabin/is-ready', [
                this.config.network.sendHost,
                this.config.network.listenPort,
                '/gabin/ready-response'
            ]);
        } catch (error) {
            this.handleError(error as Error, 'Failed to ping Gabin');
        }
    }

    private async sendOscCommand(path: string, args: any[]): Promise<void> {
        if (!this.oscClient) {
            throw new Error('OSC client not initialized');
        }

        return new Promise((resolve, reject) => {
            this.oscClient!.send(path, ...args, (error) => {
                if (error) {
                    reject(error);
                } else {
                    resolve();
                }
            });
        });
    }

    private handleOscMessage(msg: any[]): void {
        const [path, ...args] = msg;

        switch (path) {
            case '/gabin/ready-response':
                this.handleGabinReady(args[0]);
                break;
            case '/autocam/feedback':
                this.handleAutocamFeedback(args[0]);
                break;
            case '/mic/feedback':
                this.handleMicFeedback(args[0], args[1]);
                break;
            default:
                this.log(`Unhandled OSC message: ${path}`, args);
        }
    }

    private handleGabinReady(ready: any): void {
        const wasConnected = this.connectionStatus.connected;
        // Convertir en boolean pour s'assurer du type
        const isReady = Boolean(ready);
        this.updateConnectionStatus({ connected: isReady });

        if (!wasConnected && isReady) {
            this.log('Gabin connection established');
        } else if (wasConnected && !isReady) {
            this.log('Gabin connection lost');
        }
    }

    private handleAutocamFeedback(active: boolean): void {
        this.updateAutocamState(active);
    }

    private handleMicFeedback(micName: string, active: boolean): void {
        this.updateMicState(micName, active);
    }

    private updateAutocamState(active: boolean): void {
        if (this.autocamState !== active) {
            this.autocamState = active;
            this.updateState('autocam', active);
            
            this.emitEvent(MODULE_EVENTS.AUTOCAM_STATE_CHANGED, {
                active,
                timestamp: new Date(),
            });
        }
    }

    private updateMicState(micName: string, active: boolean): void {
        if (this.micStates[micName] !== active) {
            this.micStates[micName] = active;
            this.updateState(`mic_${micName}`, active);
            
            this.emitEvent(MODULE_EVENTS.MIC_STATE_CHANGED, {
                micName,
                active,
                timestamp: new Date(),
            });
        }
    }
}
