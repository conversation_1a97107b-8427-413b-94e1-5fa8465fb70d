import { Injectable, OnModuleInit } from '@nestjs/common';
import { LinkManagerService } from './services/link-manager.service';

// Types de compatibilité temporaire
export interface ServiceStatus {
    connected: boolean | undefined;
    lastSeen: Date | undefined;
}
export type ServiceName = 'gabin' | 'companion' | 'obs';
export type HubEventCallback = (event: any) => void;

/**
 * Hub Central - Orchestration pure + Compatibilité temporaire
 *
 * Responsabilité UNIQUE : Gérer les liens entre modules autonomes
 * + Méthodes de compatibilité pour les anciens services (à supprimer)
 */
@Injectable()
export class HubService implements OnModuleInit {
    // États temporaires pour compatibilité (à supprimer)
    private autocamState: boolean | null = null;
    private micStates: Record<string, boolean> = {};
    private serviceStates: Record<ServiceName, ServiceStatus> = {
        gabin: { connected: false, lastSeen: undefined },
        companion: { connected: undefined, lastSeen: undefined },
        obs: { connected: false, lastSeen: undefined },
    };

    constructor(
        private readonly linkManager: LinkManagerService,
    ) {}

    async onModuleInit() {
        console.log('[Hub] Hub orchestration initialized - managing module links only');
    }

    /**
     * Obtenir l'état de tous les modules (pour l'API /status)
     */
    getModulesStatus(): Record<string, any> {
        return this.linkManager.getModulesStatus();
    }

    /**
     * Activer/désactiver un lien entre modules
     */
    async toggleLink(linkName: string, enabled: boolean): Promise<void> {
        return this.linkManager.toggleLink(linkName, enabled);
    }

    /**
     * API de statut simplifiée
     */
    getStatus(): any {
        return {
            modules: this.getModulesStatus(),
            links: this.linkManager.getLinksStatus?.() || {},
            // Compatibilité temporaire
            autocam: this.autocamState,
            mics: this.micStates,
            services: this.serviceStates,
        };
    }

    // === MÉTHODES DE COMPATIBILITÉ TEMPORAIRE (À SUPPRIMER) ===

    onEvent(callback: HubEventCallback): void {
        console.log('[Hub] onEvent called - stub for compatibility');
    }

    setAutocam(value: boolean): void {
        this.autocamState = value;
        console.log(`[Hub] Autocam set to: ${value} (compatibility mode)`);
    }

    getAutocam(): boolean | null {
        return this.autocamState;
    }

    setMic(name: string, value: boolean): void {
        this.micStates[name] = value;
        console.log(`[Hub] Mic '${name}' set to: ${value} (compatibility mode)`);
    }

    getMic(name: string): boolean | undefined {
        return this.micStates[name];
    }

    setServiceStatus(serviceName: ServiceName, status: Partial<ServiceStatus>): void {
        this.serviceStates[serviceName] = {
            ...this.serviceStates[serviceName],
            ...status,
        };
        console.log(`[Hub] Service '${serviceName}' status updated (compatibility mode):`, status);
    }

    setServiceTimeout(serviceName: ServiceName, timeoutId: NodeJS.Timeout): void {
        console.log(`[Hub] Service '${serviceName}' timeout set (compatibility mode)`);
    }

    isServiceConnected(serviceName: ServiceName): boolean {
        return this.serviceStates[serviceName]?.connected === true;
    }

    getServiceStatus(serviceName: ServiceName): ServiceStatus {
        return this.serviceStates[serviceName];
    }
}
