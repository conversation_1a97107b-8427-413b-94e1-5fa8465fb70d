/**
 * Configuration par défaut pour OBS WebSocket
 *
 * ⚠️  NE PAS MODIFIER CE FICHIER
 *
 * Ce fichier contient la configuration par défaut du module OBS.
 * Pour personnaliser la configuration, créez un fichier `obs.local.ts`
 * dans le dossier `src/config/local/` avec vos paramètres spécifiques.
 *
 * Le fichier local sera automatiquement fusionné avec cette configuration
 * par défaut au démarrage de l'application.
 */

import { OBSConfig } from '../../obs/obs.types';

export const obsDefaultConfig: OBSConfig = {
    network: {
        host: '127.0.0.1',
        port: 4455,
        password: undefined, // Sera demandé si nécessaire
    },
    timing: {
        connectionTimeout: 5000, // 5 secondes
        reconnectInterval: 10000, // 10 secondes
        pingInterval: 30000, // 30 secondes
    },
    autoConnect: true,
};
