#!/usr/bin/env node

/**
 * Script de test pour simuler des actions Companion
 * Envoie des messages OSC vers le port d'écoute de obs-video-hub
 */

const { Client } = require('node-osc');

const HOST = '127.0.0.1';
const PORT = 33223; // Port d'écoute de obs-video-hub pour Companion

function sendAction(path, ...args) {
    return new Promise((resolve, reject) => {
        const client = new Client(HOST, PORT);
        
        console.log(`Sending OSC: ${path}`, args.length > 0 ? args : '');
        
        client.send(path, ...args, (err) => {
            client.close();
            if (err) {
                console.error('Error sending OSC:', err);
                reject(err);
            } else {
                console.log(`✅ Sent: ${path}`);
                resolve();
            }
        });
    });
}

async function testActions() {
    console.log('🧪 Testing Companion actions...\n');
    
    try {
        // Test 1: Toggle autocam
        console.log('1. Testing autocam toggle...');
        await sendAction('/action/autocam/toggle');
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Test 2: Toggle MIC1
        console.log('2. Testing MIC1 toggle...');
        await sendAction('/action/mic/MIC1/toggle');
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Test 3: Set autocam ON
        console.log('3. Testing autocam ON...');
        await sendAction('/action/autocam/on');
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Test 4: Set MIC2 OFF
        console.log('4. Testing MIC2 OFF...');
        await sendAction('/action/mic/MIC2/off');
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Test 5: Companion ready signal
        console.log('5. Testing companion ready signal...');
        await sendAction('/companion/ready');
        
        console.log('\n✅ All tests completed!');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        process.exit(1);
    }
}

// Lancer les tests
testActions();
