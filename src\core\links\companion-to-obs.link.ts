/**
 * Lien Companion → OBS
 * Transmet les commandes de Companion vers OBS
 */

import { IModuleLink, LinkConfig } from '../interfaces/link.interface';
import { IModule, IMicrophoneModule, IControlModule } from '../interfaces/module.interface';

export class CompanionToOBSLink implements IModuleLink {
    public readonly name = 'companion-to-obs';
    public readonly description = 'Transmet les commandes Companion vers OBS';
    public readonly enabled: boolean;

    private companionModule?: IControlModule;
    private obsModule?: IMicrophoneModule & { 
        setSourceVolume?: (adjustment: any) => Promise<void>;
        adjustSourceVolumePercent?: (sourceName: string, percent: number) => Promise<void>;
        setSourceMute?: (sourceName: string, muted: boolean) => Promise<void>;
        toggleSourceMute?: (sourceName: string) => Promise<boolean>;
    };
    private cleanupCallbacks: Array<() => void> = [];

    constructor(private config: LinkConfig) {
        this.enabled = config.enabled;
    }

    async initialize(modules: Map<string, IModule>): Promise<void> {
        if (!this.enabled) {
            console.log(`[${this.name}] Link is disabled`);
            return;
        }

        // Récupérer les modules nécessaires
        this.companionModule = modules.get('companion') as IControlModule;
        this.obsModule = modules.get('obs') as any; // Cast vers le type étendu

        if (!this.companionModule) {
            console.log(`[${this.name}] Companion module not available - link disabled`);
            return;
        }

        if (!this.obsModule) {
            console.log(`[${this.name}] OBS module not available - link disabled`);
            return;
        }

        console.log(`[${this.name}] Initializing link between Companion and OBS`);

        // S'abonner aux événements de Companion
        this.setupCompanionListeners();

        console.log(`[${this.name}] Link initialized successfully`);
    }

    async cleanup(): Promise<void> {
        console.log(`[${this.name}] Cleaning up link`);
        
        // Nettoyer tous les listeners
        this.cleanupCallbacks.forEach(cleanup => cleanup());
        this.cleanupCallbacks = [];

        this.companionModule = undefined;
        this.obsModule = undefined;
    }

    /**
     * Configurer les listeners sur les événements Companion
     */
    private setupCompanionListeners(): void {
        if (!this.companionModule || !this.obsModule) return;

        // Écouter les actions de Companion
        const actionListener = (event: any) => {
            if (event.type === 'companion_action') {
                this.handleCompanionAction(event.data);
            }
        };

        this.companionModule.onEvent(actionListener);

        // Stocker les callbacks pour le nettoyage
        this.cleanupCallbacks.push(
            () => console.log(`[${this.name}] Cleaned up Companion listeners`)
        );
    }

    /**
     * Traiter une action reçue de Companion
     */
    private async handleCompanionAction(actionData: any): Promise<void> {
        if (!this.obsModule) return;

        const { actionType, target, value } = actionData;

        try {
            switch (actionType) {
                case 'mic':
                    await this.handleMicAction(target, value);
                    break;
                
                case 'volume':
                    await this.handleVolumeAction(target, value);
                    break;
                
                default:
                    console.log(`[${this.name}] Unhandled action type: ${actionType}`);
            }
        } catch (error) {
            console.error(`[${this.name}] Error handling action:`, error.message);
        }
    }

    /**
     * Traiter une action micro (mute/unmute)
     */
    private async handleMicAction(sourceName: string, value: any): Promise<void> {
        if (!this.obsModule || !this.config.options?.allowMuteControl || !sourceName) {
            return;
        }

        try {
            if (value === 'toggle') {
                // Basculer le mute
                const newMuteState = await this.obsModule.toggleSourceMute?.(sourceName);
                console.log(`[${this.name}] Source '${sourceName}' mute toggled - now ${newMuteState ? 'muted' : 'unmuted'}`);
            } else if (typeof value === 'boolean') {
                // Définir un état de mute spécifique
                // Dans OBS : actif = non muted
                await this.obsModule.setSourceMute?.(sourceName, !value);
                console.log(`[${this.name}] Source '${sourceName}' ${value ? 'activated' : 'muted'}`);
            }
        } catch (error) {
            console.error(`[${this.name}] Error controlling source '${sourceName}':`, error.message);
        }
    }

    /**
     * Traiter une action de volume
     */
    private async handleVolumeAction(sourceName: string, value: any): Promise<void> {
        if (!this.obsModule || !this.config.options?.allowVolumeControl || !sourceName) {
            return;
        }

        try {
            if (typeof value === 'number') {
                if (value >= -100 && value <= 100) {
                    // Ajustement relatif en pourcentage
                    await this.obsModule.adjustSourceVolumePercent?.(sourceName, value);
                    console.log(`[${this.name}] Volume adjusted for '${sourceName}': ${value}%`);
                } else {
                    // Volume absolu en dB
                    await this.obsModule.setSourceVolume?.({ 
                        sourceName, 
                        volumeDb: value 
                    });
                    console.log(`[${this.name}] Volume set for '${sourceName}': ${value}dB`);
                }
            }
        } catch (error) {
            console.error(`[${this.name}] Error controlling volume for '${sourceName}':`, error.message);
        }
    }
}
