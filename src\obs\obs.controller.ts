import { Controller, Post, Get, Body, Param, HttpException, HttpStatus } from '@nestjs/common';
import { OBSService } from './obs.service';

/**
 * Contrôleur pour les commandes OBS
 * Expose les fonctionnalités OBS via l'API REST
 */
@Controller('obs')
export class OBSController {
    constructor(private readonly obsService: OBSService) {}

    /**
     * Ajuster le volume d'une source audio par dB
     * POST /obs/source/:sourceName/volume
     * Body: { volumeDb: number }
     */
    @Post('source/:sourceName/volume')
    async setSourceVolumeDb(@Param('sourceName') sourceName: string, @Body('volumeDb') volumeDb: number) {
        try {
            await this.obsService.setSourceVolume({ sourceName, volumeDb });
            return {
                success: true,
                message: `Volume set to ${volumeDb}dB for source "${sourceName}"`,
            };
        } catch (error) {
            throw new HttpException({ success: false, error: error.message }, HttpStatus.BAD_REQUEST);
        }
    }

    /**
     * Ajuster le volume d'une source audio par pourcentage
     * POST /obs/source/:sourceName/volume-percent
     * Body: { volumePercent: number }
     */
    @Post('source/:sourceName/volume-percent')
    async setSourceVolumePercent(@Param('sourceName') sourceName: string, @Body('volumePercent') volumePercent: number) {
        try {
            await this.obsService.setSourceVolume({ sourceName, volumePercent });
            return {
                success: true,
                message: `Volume set to ${volumePercent}% for source "${sourceName}"`,
            };
        } catch (error) {
            throw new HttpException({ success: false, error: error.message }, HttpStatus.BAD_REQUEST);
        }
    }

    /**
     * Ajuster le volume d'une source par pourcentage relatif (comme dans Companion)
     * POST /obs/source/:sourceName/adjust-volume
     * Body: { percentAdjustment: number } (ex: -100 pour mute, +50 pour augmenter de 50%)
     */
    @Post('source/:sourceName/adjust-volume')
    async adjustSourceVolume(@Param('sourceName') sourceName: string, @Body('percentAdjustment') percentAdjustment: number) {
        try {
            await this.obsService.adjustSourceVolumePercent(sourceName, percentAdjustment);
            return {
                success: true,
                message: `Volume adjusted by ${percentAdjustment}% for source "${sourceName}"`,
            };
        } catch (error) {
            throw new HttpException({ success: false, error: error.message }, HttpStatus.BAD_REQUEST);
        }
    }

    /**
     * Activer/désactiver le mute d'une source
     * POST /obs/source/:sourceName/mute
     * Body: { muted: boolean }
     */
    @Post('source/:sourceName/mute')
    async setSourceMute(@Param('sourceName') sourceName: string, @Body('muted') muted: boolean) {
        try {
            await this.obsService.setSourceMute(sourceName, muted);
            return {
                success: true,
                message: `Source "${sourceName}" ${muted ? 'muted' : 'unmuted'}`,
            };
        } catch (error) {
            throw new HttpException({ success: false, error: error.message }, HttpStatus.BAD_REQUEST);
        }
    }

    /**
     * Basculer le mute d'une source
     * POST /obs/source/:sourceName/toggle-mute
     */
    @Post('source/:sourceName/toggle-mute')
    async toggleSourceMute(@Param('sourceName') sourceName: string) {
        try {
            const newMuteState = await this.obsService.toggleSourceMute(sourceName);
            return {
                success: true,
                muted: newMuteState,
                message: `Source "${sourceName}" ${newMuteState ? 'muted' : 'unmuted'}`,
            };
        } catch (error) {
            throw new HttpException({ success: false, error: error.message }, HttpStatus.BAD_REQUEST);
        }
    }

    /**
     * Obtenir les informations d'une source audio
     * GET /obs/source/:sourceName/info
     */
    @Get('source/:sourceName/info')
    async getSourceInfo(@Param('sourceName') sourceName: string) {
        try {
            const info = await this.obsService.getSourceInfo(sourceName);
            return {
                success: true,
                data: info,
            };
        } catch (error) {
            throw new HttpException({ success: false, error: error.message }, HttpStatus.BAD_REQUEST);
        }
    }
}
