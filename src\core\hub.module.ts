import { Modu<PERSON> } from '@nestjs/common';
import { HubService } from './hub.service';
import { HubController } from './hub.controller';
import { ModulesController } from './modules.controller';
import { LinkManagerService } from './services/link-manager.service';

@Module({
    providers: [HubService, LinkManagerService],
    controllers: [HubController, ModulesController],
    exports: [HubService, LinkManagerService],
})
export class HubModule {}
