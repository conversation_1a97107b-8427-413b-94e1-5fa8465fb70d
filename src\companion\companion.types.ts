/**
 * Types et interfaces pour le module Companion
 */

/**
 * Types de feedback supportés par Companion
 */
export type CompanionFeedbackType = 'autocam' | 'mic';

/**
 * Configuration d'un feedback Companion
 */
export interface CompanionFeedbackConfig {
    type: CompanionFeedbackType;
    path: string;
    micName?: string;
}

/**
 * Configuration réseau pour Companion
 */
export interface CompanionNetworkConfig {
    listenPort: number;
    host: string;
    port: number;
}

/**
 * Configuration des timeouts et intervalles pour Companion
 */
export interface CompanionTimingConfig {
    connectionCheckInterval: number;
    initialStateDelay: number;
}

/**
 * Configuration complète pour Companion
 */
export interface CompanionConfig {
    network: CompanionNetworkConfig;
    timing: CompanionTimingConfig;
    feedbacks: CompanionFeedbackConfig[];
}

/**
 * Target de feedback interne (utilisé par CompanionTransportService)
 */
export interface FeedbackTarget {
    host: string;
    port: number;
    path: string;
    type: CompanionFeedbackType;
    micName?: string;
}

/**
 * Types de messages OSC reçus de Companion
 */
export type CompanionOscMessage = [string, string, number, string, string];

/**
 * Types d'actions supportées par Companion
 */
export type CompanionActionType = 'toggle' | 'on' | 'off';

/**
 * Valeurs de feedback OSC
 */
export const FEEDBACK_VALUES = {
    OFF: 0,
    ON: 1,
    UNDEFINED: 2, // État gris/indéfini quand Gabin est déconnecté
} as const;

export type FeedbackValue = (typeof FEEDBACK_VALUES)[keyof typeof FEEDBACK_VALUES];
