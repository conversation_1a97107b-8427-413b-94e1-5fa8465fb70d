/**
 * Module Companion autonome
 * Gère sa propre connexion OSC, ses états et sa logique
 */

import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { Server, Client } from 'node-osc';
import { BaseModule } from '../core/base/base-module';
import { IControlModule, MODULE_EVENTS } from '../core/interfaces/module.interface';
import { companionConfig, modulesConfig } from '../config';
import { FeedbackTarget, CompanionOscMessage } from './companion.types';

@Injectable()
export class CompanionModuleService 
    extends BaseModule 
    implements IControlModule, OnModuleInit, OnModuleDestroy {
    
    private oscServer?: Server;
    private oscClient?: Client;
    private readonly config = companionConfig;
    private readonly feedbackTargets: FeedbackTarget[] = [];

    // États internes du module
    private lastReadySignal?: Date;

    constructor() {
        super('companion', modulesConfig.companion.enabled);
        this.log('Companion module created', { enabled: this.enabled });
        this.initializeFeedbackTargets();
    }

    async onModuleInit() {
        if (this.enabled) {
            await this.start();
        } else {
            this.log('Module disabled - not starting');
        }
    }

    async onModuleDestroy() {
        await this.stop();
    }

    async start(): Promise<void> {
        if (!this.enabled) {
            this.log('Cannot start - module is disabled');
            return;
        }

        try {
            this.log('Starting Companion module...');
            
            await this.startOscServer();
            this.startOscClient();
            
            this.updateConnectionStatus({ connected: true });
            this.log('Companion module started successfully');
            
        } catch (error) {
            this.handleError(error, 'Failed to start Companion module');
        }
    }

    async stop(): Promise<void> {
        this.log('Stopping Companion module...');
        
        if (this.oscServer) {
            this.oscServer.close();
            this.oscServer = undefined;
        }

        if (this.oscClient) {
            this.oscClient.close();
            this.oscClient = undefined;
        }

        this.updateConnectionStatus({ connected: false });
        this.log('Companion module stopped');
    }

    // === INTERFACE IControlModule ===

    async sendFeedback(type: string, value: any, target?: string): Promise<void> {
        if (!this.isReady()) {
            this.log('Cannot send feedback - module not ready');
            return;
        }

        try {
            const feedbackValue = this.convertToFeedbackValue(value);
            
            // Trouver les targets correspondantes
            const targets = this.feedbackTargets.filter(t => {
                if (type === 'autocam') return t.type === 'autocam';
                if (type === 'mic') return t.type === 'mic' && t.micName === target;
                return false;
            });

            // Envoyer le feedback vers chaque target
            for (const feedbackTarget of targets) {
                await this.sendOscFeedback(feedbackTarget, feedbackValue);
            }

            this.log(`Feedback sent: ${type}${target ? `(${target})` : ''} = ${value}`);

        } catch (error) {
            this.handleError(error, `Failed to send feedback: ${type}`);
        }
    }

    // === MÉTHODES PUBLIQUES ===

    /**
     * Traiter un signal "ready" de Companion
     */
    handleCompanionReady(): void {
        this.lastReadySignal = new Date();
        this.updateState('lastReady', this.lastReadySignal);
        
        this.log('Companion ready signal received');
        
        // Émettre un événement pour que les liens puissent réagir
        this.emitEvent('companion_ready', {
            timestamp: this.lastReadySignal,
        });
    }

    /**
     * Traiter une action reçue de Companion
     */
    async handleCompanionAction(actionType: string, target?: string, value?: any): Promise<void> {
        this.log(`Action received: ${actionType}${target ? `(${target})` : ''}`, { value });

        // Émettre un événement pour que les liens puissent traiter l'action
        this.emitEvent('companion_action', {
            actionType,
            target,
            value,
            timestamp: new Date(),
        });
    }

    // === MÉTHODES PRIVÉES ===

    private async startOscServer(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.oscServer = new Server(this.config.network.listenPort, '0.0.0.0', () => {
                this.log(`OSC server listening on port ${this.config.network.listenPort}`);
                resolve();
            });

            this.oscServer.on('error', (error) => {
                reject(new Error(`OSC server error: ${error.message}`));
            });

            this.oscServer.on('message', (msg: CompanionOscMessage) => {
                this.handleOscMessage(msg);
            });
        });
    }

    private startOscClient(): void {
        this.oscClient = new Client(this.config.network.host, this.config.network.port);
        this.log(`OSC client connected to ${this.config.network.host}:${this.config.network.port}`);
    }

    private initializeFeedbackTargets(): void {
        this.log('Initializing feedback targets...');
        
        this.config.feedbacks.forEach((feedback) => {
            const target: FeedbackTarget = {
                host: this.config.network.host,
                port: this.config.network.port,
                path: feedback.path,
                type: feedback.type,
                micName: feedback.micName,
            };

            this.feedbackTargets.push(target);
            this.log(`Added feedback target: ${feedback.type} → ${feedback.path}`);
        });

        this.log(`Initialized ${this.feedbackTargets.length} feedback targets`);
    }

    private handleOscMessage(msg: CompanionOscMessage): void {
        const [path, ...args] = msg;

        if (path.startsWith('/action/')) {
            this.handleActionMessage(msg);
        } else if (path === '/companion/ready') {
            this.handleCompanionReady();
        } else {
            this.log(`Unhandled OSC message: ${path}`, args);
        }
    }

    private handleActionMessage(msg: CompanionOscMessage): void {
        const [path, action, value, target] = msg;

        // Extraire le type d'action du path
        const actionMatch = path.match(/^\/action\/(.+)$/);
        if (!actionMatch) {
            this.log(`Invalid action path: ${path}`);
            return;
        }

        const actionType = actionMatch[1];
        this.handleCompanionAction(actionType, target, value);
    }

    private async sendOscFeedback(target: FeedbackTarget, value: number): Promise<void> {
        if (!this.oscClient) {
            throw new Error('OSC client not initialized');
        }

        return new Promise((resolve, reject) => {
            this.oscClient!.send(target.path, value, (error) => {
                if (error) {
                    reject(error);
                } else {
                    resolve();
                }
            });
        });
    }

    private convertToFeedbackValue(value: any): number {
        if (value === undefined || value === null) {
            return 2; // État "undefined" (bouton gris)
        }
        return value ? 1 : 0; // true = 1, false = 0
    }
}
