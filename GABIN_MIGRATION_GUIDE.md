# Guide de migration : Désactivation de Gabin

## ✅ Migration terminée !

Votre Hub fonctionne maintenant **sans Gabin** et utilise **OBS directement** pour la gestion des micros et caméras.

## 🔧 Configuration actuelle

### Modules activés/désactivés
- ❌ **Gabin** : Dés<PERSON><PERSON><PERSON> (`enabled: false`)
- ✅ **OBS** : Activé et connecté
- ✅ **Companion** : Activé (fonctionne sans Gabin)

### Fichier de configuration
`src/config/local/modules.local.ts` :
```typescript
gabin: {
    enabled: false,  // Gabin désactivé
    description: "Module Gabin désactivé - utilisation d'OBS directement"
}
```

## 🔄 Changements de comportement

### Avant (avec Gabin)
```
Companion → Gabin → Caméras/Micros
```

### Maintenant (sans Gabin)
```
Companion → Hub → OBS → Micros
                ↓
            États centralisés
```

## 🎛️ Contrôles disponibles

### 1. Contrôle des micros via OBS API
```bash
# Obtenir l'état d'un micro
GET http://localhost:3000/obs/source/MIC%20Invite%201/info

# Mute/Unmute un micro
POST http://localhost:3000/obs/source/MIC%20Invite%201/mute
{ "muted": true }

# Ajuster le volume
POST http://localhost:3000/obs/source/MIC%20Invite%201/adjust-volume
{ "percentAdjustment": -100 }  # Mute complet
```

### 2. Contrôle via Companion (boutons)
- Les boutons Companion fonctionnent toujours
- Ils mettent à jour directement les états dans le Hub
- Plus de dépendance à Gabin

## 🚀 Prochaines étapes

### 1. Synchronisation OBS → Hub
Pour que les changements dans OBS remontent au Hub :
```typescript
// Dans OBSService, écouter les événements OBS
obs.on('InputMuteStateChanged', (data) => {
    // Mettre à jour l'état dans le Hub
    hubService.setMic(data.inputName, !data.inputMuted);
});
```

### 2. Automations caméras
```typescript
// Exemple : Micro activé → Changer de caméra
hubService.onEvent((event) => {
    if (event.type === 'mic_changed' && event.data.value) {
        // Déclencher changement de caméra via OBS
        obsService.setCurrentScene(`Camera_${event.data.name}`);
    }
});
```

### 3. Interface de contrôle
- Créer une interface web pour contrôler OBS
- Afficher les états en temps réel
- Boutons de contrôle direct

## 🔧 Réactivation de Gabin

Si vous voulez réactiver Gabin plus tard :

1. **Modifier la configuration** :
```typescript
// src/config/local/modules.local.ts
gabin: {
    enabled: true,
    description: "Module Gabin réactivé"
}
```

2. **Redémarrer l'application** :
```bash
npm run start:dev
```

3. **Vérifier les logs** :
```
[app] Loading Gabin module...
[gabin] Starting Gabin connection monitoring...
```

## 📊 Statut actuel

Vérifiez le statut via :
```bash
curl http://localhost:3000/status
```

Résultat attendu :
- `gabin.connected: false` (normal, désactivé)
- `obs.connected: true` (OBS connecté)
- `companion` présent (feedbacks actifs)

## 🎯 Avantages de cette architecture

1. **Flexibilité** : Activation/désactivation facile des modules
2. **Performance** : Moins de services actifs = moins de ressources
3. **Simplicité** : Communication directe Hub ↔ OBS
4. **Évolutivité** : Facile d'ajouter de nouveaux modules
5. **Maintenance** : Code plus clair et modulaire
