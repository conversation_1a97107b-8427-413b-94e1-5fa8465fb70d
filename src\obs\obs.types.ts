/**
 * Configuration pour la connexion OBS WebSocket
 */
export interface OBSNetworkConfig {
    host: string;
    port: number;
    password?: string;
}

/**
 * Configuration des timeouts et intervalles
 */
export interface OBSTimingConfig {
    connectionTimeout: number;
    reconnectInterval: number;
    pingInterval: number;
}

/**
 * Configuration complète du module OBS
 */
export interface OBSConfig {
    network: OBSNetworkConfig;
    timing: OBSTimingConfig;
    autoConnect: boolean;
}

/**
 * Statut de connexion OBS
 */
export interface OBSConnectionStatus {
    connected: boolean;
    obsWebSocketVersion?: string;
    rpcVersion?: number;
    lastError?: string;
}

/**
 * Informations sur une source audio OBS
 */
export interface OBSAudioSource {
    inputName: string;
    inputVolumeMul: number; // Volume multiplier (0.0 to 1.0+)
    inputVolumeDb: number; // Volume in dB
    inputMuted: boolean;
}

/**
 * Paramètres pour ajuster le volume d'une source
 */
export interface OBSVolumeAdjustment {
    sourceName: string;
    volumeDb?: number; // Volume en dB (-100 à +26)
    volumeMul?: number; // Volume multiplier (0.0 à 1.0+)
    volumePercent?: number; // Volume en pourcentage (0 à 100)
}

/**
 * Événements émis par le service OBS
 */
export interface OBSEvent {
    type: 'connection_changed' | 'volume_changed' | 'mute_changed';
    data: {
        connected?: boolean;
        sourceName?: string;
        volume?: number;
        muted?: boolean;
        error?: string;
    };
}

/**
 * Callback pour les événements OBS
 */
export type OBSEventCallback = (event: OBSEvent) => void;
