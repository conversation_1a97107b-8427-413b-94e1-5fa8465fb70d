import { Injectable, OnModuleInit, Optional } from '@nestjs/common';
import { Server, Client, Message } from 'node-osc';
import { GabinService } from '../gabin/gabin.service';
import { HubService } from '../core/hub.service';
import { companionConfig, modulesConfig } from '../config';
import { FeedbackTarget } from './companion.types';

@Injectable()
export class CompanionTransportService implements OnModuleInit {
    private oscServer: Server;
    private readonly config = companionConfig;
    private readonly feedbackTargets: FeedbackTarget[] = [];

    // Monitoring simple de la connexion Gabin
    private lastGabinState: boolean | null = null;

    constructor(
        @Optional() private readonly gabinService: GabinService | null,
        private readonly hubService: HubService, // Temporaire pour compatibilité
    ) {
        console.log(`[Companion] Loaded config for ${this.config.network.host}:${this.config.network.port} with ${this.config.feedbacks.length} feedbacks`);
    }

    onModuleInit() {
        this.startOscServer();

        if (modulesConfig.gabin.enabled && this.gabinService) {
            this.setupGabinListeners();
        } else {
            console.log('[Companion] Gabin module disabled - using Hub events only');
        }

        this.initializeCompanionTargets();
        this.startConnectionMonitoring();
    }

    private startOscServer() {
        this.oscServer = new Server(this.config.network.listenPort, '0.0.0.0', () => {
            console.log(`[Companion] OSC server listening on port ${this.config.network.listenPort}`);
        });

        this.oscServer.on('message', (msg: [string, string, number, string, string]) => {
            const [path] = msg;

            if (path.startsWith('/action/')) {
                this.handleActionMessage(msg);
            } else if (path === '/companion/ready') {
                this.handleCompanionReady();
            } else {
                console.log(`[Companion] Unhandled message path: ${path}`);
            }
        });
    }

    private setupGabinListeners() {
        // TODO: Utiliser le nouveau système de modules autonomes avec événements
        console.log('[Companion] Gabin listeners setup - TODO: implement with new module system');
    }

    private handleActionMessage(msg: [string, ...any[]]) {
        const [path, ...args] = msg;
        console.log(`[Companion] Received action: ${path}`, args);

        if (path === '/action/autocam/toggle') {
            this.toggleAutocam();
        } else if (path === '/action/autocam/on') {
            this.setAutocam(true);
        } else if (path === '/action/autocam/off') {
            this.setAutocam(false);
        } else if (path.startsWith('/action/mic/') && path.includes('/toggle')) {
            // Ex: /action/mic/MIC1/toggle
            const micName = path.split('/')[3];
            if (micName) {
                this.toggleMic(micName);
            }
        } else if (path.startsWith('/action/mic/') && path.includes('/on')) {
            // Ex: /action/mic/MIC1/on
            const micName = path.split('/')[3];
            if (micName) {
                this.setMic(micName, true);
            }
        } else if (path.startsWith('/action/mic/') && path.includes('/off')) {
            // Ex: /action/mic/MIC1/off
            const micName = path.split('/')[3];
            if (micName) {
                this.setMic(micName, false);
            }
        } else {
            console.log(`[Companion] Unhandled action path: ${path}`);
        }
    }

    /**
     * Fonction centralisée pour envoyer les feedbacks
     */
    private sendFeedback(type: 'autocam' | 'mic', state: boolean, micName?: string) {
        // Filtrer les targets selon le type et le nom du micro si applicable
        const relevantTargets = this.feedbackTargets.filter((target) => {
            if (target.type !== type) return false;
            if (type === 'mic' && target.micName !== micName) return false;
            return true;
        });

        if (relevantTargets.length === 0) {
            return;
        }

        // Log seulement pour les changements d'état importants
        console.log(`[Companion] ${type}${micName ? ` ${micName}` : ''} → ${state ? 'ON' : 'OFF'}`);

        relevantTargets.forEach(({ host, port, path }) => {
            this.sendOscMessage(host, port, path, state ? 1 : 0, type);
        });
    }

    /**
     * Envoie un message OSC à un target spécifique
     */
    private sendOscMessage(host: string, port: number, path: string, value: number, type: string) {
        try {
            const client = new Client(host, port);
            const msg = new Message(path, value);

            client.send(msg, (err) => {
                if (err) {
                    console.error(`[Companion] Failed to send ${type} feedback to ${host}:${port}${path}:`, err);
                }
                // Suppression du log de succès - trop verbeux
                client.close();
            });
        } catch (error) {
            console.error(`[Companion] Error sending ${type} feedback to ${host}:${port}${path}:`, error);
        }
    }

    // === ACTIONS POUR CONTRÔLER GABIN ===

    /**
     * Toggle l'état de l'autocam
     */
    private toggleAutocam() {
        const currentState = this.hubService.getAutocam();
        const newState = currentState === null ? true : !currentState;
        console.log(`[Companion] Toggling autocam: ${currentState} → ${newState}`);

        if (this.gabinService) {
            this.gabinService.sendAutocamCommand(newState);
        } else {
            // Sans Gabin, on met à jour directement l'état dans le Hub
            this.hubService.setAutocam(newState);
            console.log('[Companion] Autocam updated directly in Hub (Gabin disabled)');
        }
    }

    /**
     * Définit l'état de l'autocam
     */
    private setAutocam(state: boolean) {
        console.log(`[Companion] Setting autocam to: ${state}`);

        if (this.gabinService) {
            this.gabinService.sendAutocamCommand(state);
        } else {
            // Sans Gabin, on met à jour directement l'état dans le Hub
            this.hubService.setAutocam(state);
            console.log('[Companion] Autocam updated directly in Hub (Gabin disabled)');
        }
    }

    /**
     * Toggle l'état d'un micro
     */
    private toggleMic(micName: string) {
        const currentState = this.hubService.getMic(micName);
        const newState = currentState === undefined ? true : !currentState;
        console.log(`[Companion] Toggling mic '${micName}': ${currentState} → ${newState}`);

        if (this.gabinService) {
            this.gabinService.sendMicCommand(micName, newState);
        } else {
            // Sans Gabin, on met à jour directement l'état dans le Hub
            this.hubService.setMic(micName, newState);
            console.log(`[Companion] Mic '${micName}' updated directly in Hub (Gabin disabled)`);
        }
    }

    /**
     * Définit l'état d'un micro
     */
    private setMic(micName: string, state: boolean) {
        console.log(`[Companion] Setting mic '${micName}' to: ${state}`);

        if (this.gabinService) {
            this.gabinService.sendMicCommand(micName, state);
        } else {
            // Sans Gabin, on met à jour directement l'état dans le Hub
            this.hubService.setMic(micName, state);
            console.log(`[Companion] Mic '${micName}' updated directly in Hub (Gabin disabled)`);
        }
    }

    // === GESTION AUTOMATIQUE DES TARGETS COMPANION ===

    /**
     * Initialise les targets Companion configurés au démarrage
     */
    private initializeCompanionTargets() {
        console.log('[Companion] Initializing configured targets...');

        this.config.feedbacks.forEach((feedback) => {
            const target: FeedbackTarget = {
                host: this.config.network.host,
                port: this.config.network.port,
                path: feedback.path,
                type: feedback.type,
                ...(feedback.micName ? { micName: feedback.micName } : {}),
            };

            this.feedbackTargets.push(target);
            console.log(`[Companion] Added configured target: ${feedback.type}${feedback.micName ? ` (${feedback.micName})` : ''} → ${feedback.path}`);
        });

        // Envoyer immédiatement les états (connectés ou déconnectés)
        setTimeout(() => {
            this.sendCurrentStatesOrDisconnected();
        }, this.config.timing.initialStateDelay);
    }

    /**
     * Gère le signal "ready" de Companion
     */
    private handleCompanionReady() {
        console.log('[Companion] Received ready signal - sending current states');

        // Mettre à jour seulement le lastSeen de Companion (pas le connected car on ne peut pas détecter les déconnexions)
        this.hubService.setServiceStatus('companion', {});

        this.sendCurrentStatesOrDisconnected();
    }

    /**
     * Envoie les états actuels si Gabin est connecté, sinon envoie des états déconnectés
     */
    private sendCurrentStatesOrDisconnected() {
        if (modulesConfig.gabin.enabled && this.hubService.isServiceConnected('gabin')) {
            console.log('[Companion] Gabin connected - sending current states');
            this.sendAllCurrentStates();
        } else if (!modulesConfig.gabin.enabled) {
            console.log('[Companion] Gabin disabled - sending current Hub states');
            this.sendAllCurrentStates(); // Envoyer les états du Hub même sans Gabin
        } else {
            console.log('[Companion] Gabin not connected - sending disconnected states');
            this.sendDisconnectedStates();
        }
    }

    /**
     * Envoie tous les états actuels vers Companion
     */
    private sendAllCurrentStates() {
        // Si Gabin est activé, vérifier qu'il est connecté
        if (modulesConfig.gabin.enabled && !this.hubService.isServiceConnected('gabin')) {
            return;
        }

        console.log('[Companion] Sending current states to Companion');

        // Envoyer l'état de l'autocam
        const autocamState = this.hubService.getAutocam();
        if (autocamState !== null) {
            this.sendFeedback('autocam', autocamState);
        }

        // Envoyer l'état de tous les micros configurés
        this.config.feedbacks
            .filter((f) => f.type === 'mic' && f.micName)
            .forEach((feedback) => {
                const micState = this.hubService.getMic(feedback.micName!);
                if (micState !== undefined) {
                    this.sendFeedback('mic', micState, feedback.micName);
                }
            });
    }

    /**
     * Envoie des états "déconnectés" pour mettre les boutons en gris
     */
    private sendDisconnectedStates() {
        console.log('[Companion] Sending disconnected states (buttons will be gray)');

        // Pour chaque feedback configuré, envoyer une valeur "undefined" (2)
        this.config.feedbacks.forEach((feedback) => {
            this.sendUndefinedFeedback(feedback.type, feedback.micName);
        });
    }

    /**
     * Envoie un feedback "indéfini" (valeur 2 = bouton gris)
     */
    private sendUndefinedFeedback(type: 'autocam' | 'mic', micName?: string) {
        // Filtrer les targets selon le type et le nom du micro si applicable
        const relevantTargets = this.feedbackTargets.filter((target) => {
            if (target.type !== type) return false;
            if (type === 'mic' && target.micName !== micName) return false;
            return true;
        });

        // Suppression du log verbeux - l'info importante est dans sendDisconnectedStates()

        relevantTargets.forEach(({ host, port, path }) => {
            // Valeur 2 = état indéfini (bouton gris dans Companion)
            this.sendOscMessage(host, port, path, 2, type);
        });
    }

    /**
     * Démarre le monitoring simple de la connexion Gabin
     */
    private startConnectionMonitoring() {
        console.log('[Companion] Starting Gabin connection monitoring...');

        // Vérifier selon la configuration
        setInterval(() => {
            this.checkGabinConnectionChange();
        }, this.config.timing.connectionCheckInterval);
    }

    /**
     * Vérifie si l'état de connexion Gabin a changé
     */
    private checkGabinConnectionChange() {
        const currentState = this.hubService.isServiceConnected('gabin');

        if (this.lastGabinState !== currentState) {
            console.log(`[Companion] Gabin connection changed: ${this.lastGabinState} → ${currentState}`);
            this.lastGabinState = currentState;

            // Envoyer les nouveaux états selon la connexion
            this.sendCurrentStatesOrDisconnected();
        }
    }
}
